#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取腾讯控股(00700)的港股通持仓数据并添加到现有CSV文件中
"""

import akshare as ak
import pandas as pd
import os
from datetime import datetime
import time

def get_tencent_hsgt_data():
    """
    获取腾讯控股的港股通持仓数据
    """
    print("正在获取腾讯控股(00700)的港股通持仓数据...")
    
    try:
        # 获取港股通持股数据
        hsgt_data = ak.stock_hsgt_individual_em(symbol='00700')
        print(f"成功获取港股通数据，共 {len(hsgt_data)} 条记录")
        
        if len(hsgt_data) == 0:
            print("警告: 获取到的数据为空")
            return None
            
        # 显示数据信息
        print("数据列名:", list(hsgt_data.columns))
        print("数据日期范围:", hsgt_data['持股日期'].min(), "到", hsgt_data['持股日期'].max())
        print("最新几条数据:")
        print(hsgt_data.head())
        
        return hsgt_data
        
    except Exception as e:
        print(f"获取港股通数据失败: {e}")
        return None

def load_existing_data(file_path):
    """
    加载现有的CSV数据
    """
    if os.path.exists(file_path):
        try:
            existing_data = pd.read_csv(file_path)
            print(f"成功加载现有数据，共 {len(existing_data)} 条记录")
            print("现有数据日期范围:", existing_data['time_key'].min(), "到", existing_data['time_key'].max())
            return existing_data
        except Exception as e:
            print(f"加载现有数据失败: {e}")
            return None
    else:
        print(f"文件 {file_path} 不存在")
        return None

def merge_hsgt_data(existing_data, hsgt_data):
    """
    将港股通数据合并到现有数据中
    """
    if existing_data is None or hsgt_data is None:
        return None
    
    try:
        # 转换港股通数据的日期格式以匹配现有数据
        hsgt_data_processed = hsgt_data.copy()
        hsgt_data_processed['time_key'] = pd.to_datetime(hsgt_data_processed['持股日期']).dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # 重命名列以便合并
        hsgt_columns_mapping = {
            '持股日期': 'hsgt_date',
            '当日收盘价': 'hsgt_close_price',
            '当日涨跌幅': 'hsgt_change_rate',
            '持股数量': 'hsgt_holding_shares',
            '持股市值': 'hsgt_holding_value',
            '持股数量占A股百分比': 'hsgt_holding_ratio',
            '持股市值变化-1日': 'hsgt_value_change_1d',
            '持股市值变化-5日': 'hsgt_value_change_5d',
            '持股市值变化-10日': 'hsgt_value_change_10d'
        }
        
        hsgt_data_processed = hsgt_data_processed.rename(columns=hsgt_columns_mapping)
        
        # 基于日期合并数据
        merged_data = pd.merge(
            existing_data, 
            hsgt_data_processed[['time_key'] + list(hsgt_columns_mapping.values())], 
            on='time_key', 
            how='left'
        )
        
        print(f"数据合并完成，合并后共 {len(merged_data)} 条记录")
        
        # 显示合并后有港股通数据的记录数量
        hsgt_records = merged_data[merged_data['hsgt_holding_shares'].notna()]
        print(f"其中包含港股通数据的记录: {len(hsgt_records)} 条")
        
        if len(hsgt_records) > 0:
            print("港股通数据日期范围:", hsgt_records['time_key'].min(), "到", hsgt_records['time_key'].max())
        
        return merged_data
        
    except Exception as e:
        print(f"合并数据失败: {e}")
        return None

def save_merged_data(merged_data, file_path):
    """
    保存合并后的数据
    """
    if merged_data is None:
        return False
    
    try:
        # 备份原文件
        backup_path = file_path + '.backup.' + datetime.now().strftime('%Y%m%d_%H%M%S')
        if os.path.exists(file_path):
            os.rename(file_path, backup_path)
            print(f"原文件已备份为: {backup_path}")
        
        # 保存新数据
        merged_data.to_csv(file_path, index=False, encoding='utf-8')
        print(f"数据已成功保存到: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("腾讯控股(00700)港股通数据获取和合并工具")
    print("=" * 60)
    
    # 文件路径
    csv_file_path = "/Users/<USER>/AI/Cursor/Futu/data/H_daily/00700.csv"
    
    # 步骤1: 获取港股通数据
    hsgt_data = get_tencent_hsgt_data()
    if hsgt_data is None:
        print("无法获取港股通数据，程序退出")
        return
    
    print("\n" + "-" * 40)
    
    # 步骤2: 加载现有数据
    existing_data = load_existing_data(csv_file_path)
    if existing_data is None:
        print("无法加载现有数据，程序退出")
        return
    
    print("\n" + "-" * 40)
    
    # 步骤3: 合并数据
    merged_data = merge_hsgt_data(existing_data, hsgt_data)
    if merged_data is None:
        print("数据合并失败，程序退出")
        return
    
    print("\n" + "-" * 40)
    
    # 步骤4: 保存数据
    success = save_merged_data(merged_data, csv_file_path)
    if success:
        print("\n✅ 港股通数据已成功添加到CSV文件中！")
        
        # 显示最终统计信息
        hsgt_records = merged_data[merged_data['hsgt_holding_shares'].notna()]
        print(f"\n📊 最终统计:")
        print(f"   总记录数: {len(merged_data)}")
        print(f"   包含港股通数据的记录: {len(hsgt_records)}")
        if len(hsgt_records) > 0:
            print(f"   港股通数据覆盖率: {len(hsgt_records)/len(merged_data)*100:.1f}%")
            print(f"   最新港股通持股数量: {hsgt_records.iloc[-1]['hsgt_holding_shares']:,.0f} 股")
            print(f"   最新港股通持股市值: {hsgt_records.iloc[-1]['hsgt_holding_value']:,.0f} 港元")
    else:
        print("\n❌ 保存数据失败")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
