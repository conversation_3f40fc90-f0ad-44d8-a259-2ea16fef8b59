#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和修复缺失的港股通数据
"""

import os
import sys
import pandas as pd
import glob
import datetime
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from data.get_daily_data_final import get_hsgt_data_for_stock, merge_hsgt_data_with_stock_data

def check_and_fix_hsgt_data():
    """
    检查和修复缺失的港股通数据
    """
    print("=" * 60)
    print("检查和修复缺失的港股通数据")
    print("=" * 60)
    
    # 获取所有CSV文件
    csv_files = glob.glob('data/H_daily/*.csv')
    print(f"找到 {len(csv_files)} 个CSV文件")
    
    # 统计信息
    total_files = 0
    files_with_hsgt_data = 0
    files_with_july28_hsgt = 0
    files_fixed = 0
    files_no_hsgt_available = 0
    
    # 需要修复的股票列表
    stocks_to_fix = []
    
    print(f"\n🔍 第一轮检查：识别需要修复的股票...")
    
    for csv_file in csv_files:
        try:
            stock_code = os.path.basename(csv_file).replace('.csv', '')
            df = pd.read_csv(csv_file)
            total_files += 1
            
            # 检查是否有港股通数据
            if 'hsgt_holding_shares' in df.columns:
                hsgt_records = df[df['hsgt_holding_shares'].notna()]
                
                if len(hsgt_records) > 0:
                    files_with_hsgt_data += 1
                    
                    # 检查是否有7/28的港股通数据
                    july28_hsgt = df[(df['time_key'].str.contains('2025-07-28')) & (df['hsgt_holding_shares'].notna())]
                    if len(july28_hsgt) > 0:
                        files_with_july28_hsgt += 1
                    else:
                        # 有港股通数据但没有7/28的，可能需要更新
                        latest_hsgt_date = hsgt_records['time_key'].max()
                        if latest_hsgt_date < '2025-07-28':
                            stocks_to_fix.append((stock_code, 'update_needed'))
                else:
                    # 有港股通列但没有数据，需要检查是否能获取到数据
                    stocks_to_fix.append((stock_code, 'no_data'))
            else:
                # 没有港股通列，需要添加
                stocks_to_fix.append((stock_code, 'no_columns'))
                
        except Exception as e:
            print(f"❌ 处理 {stock_code} 时出错: {e}")
    
    print(f"\n📊 检查结果:")
    print(f"总文件数: {total_files}")
    print(f"有港股通数据: {files_with_hsgt_data}")
    print(f"有7/28港股通数据: {files_with_july28_hsgt}")
    print(f"需要修复的股票: {len(stocks_to_fix)}")
    
    if len(stocks_to_fix) == 0:
        print("✅ 所有股票的港股通数据都是最新的！")
        return
    
    print(f"\n🔧 第二轮处理：修复缺失的港股通数据...")
    print(f"将处理 {min(len(stocks_to_fix), 20)} 只股票（限制数量避免频率限制）")
    
    for i, (stock_code, issue_type) in enumerate(stocks_to_fix[:20]):
        print(f"\n处理 {i+1}/{min(len(stocks_to_fix), 20)}: {stock_code} ({issue_type})")
        
        try:
            # 获取港股通数据
            hsgt_data = get_hsgt_data_for_stock(stock_code)
            
            if hsgt_data is not None and not hsgt_data.empty:
                print(f"  ✅ 获取到港股通数据 {len(hsgt_data)} 条")
                
                # 读取现有股票数据
                csv_file = f'data/H_daily/{stock_code}.csv'
                existing_data = pd.read_csv(csv_file)
                
                # 合并数据
                merged_data = merge_hsgt_data_with_stock_data(existing_data, hsgt_data)
                
                # 检查合并结果
                hsgt_records = merged_data[merged_data['hsgt_holding_shares'].notna()]
                if len(hsgt_records) > 0:
                    # 备份原文件
                    backup_file = csv_file + f'.backup.{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}'
                    existing_data.to_csv(backup_file, index=False)
                    
                    # 保存更新后的数据
                    merged_data.to_csv(csv_file, index=False)
                    
                    latest_hsgt = hsgt_records.iloc[-1]
                    print(f"  ✅ 已更新，最新数据: {latest_hsgt['time_key']} - {latest_hsgt['hsgt_holding_shares']:,.0f} 股")
                    files_fixed += 1
                else:
                    print(f"  ❌ 合并后仍无港股通数据")
            else:
                print(f"  ⚠️  该股票确实无港股通数据")
                files_no_hsgt_available += 1
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
        
        # 添加延迟避免频率限制
        if i < min(len(stocks_to_fix), 20) - 1:
            time.sleep(1)
    
    print(f"\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)
    print(f"成功修复: {files_fixed} 只股票")
    print(f"确认无港股通数据: {files_no_hsgt_available} 只股票")
    
    if len(stocks_to_fix) > 20:
        print(f"剩余 {len(stocks_to_fix) - 20} 只股票未处理，可再次运行脚本继续处理")

def check_specific_date_coverage(target_date='2025-07-28'):
    """
    检查特定日期的港股通数据覆盖情况
    """
    print(f"\n🔍 检查 {target_date} 的港股通数据覆盖情况...")
    
    csv_files = glob.glob('data/H_daily/*.csv')
    stocks_with_target_date = []
    stocks_without_target_date = []
    
    for csv_file in csv_files[:50]:  # 检查前50个文件
        try:
            stock_code = os.path.basename(csv_file).replace('.csv', '')
            df = pd.read_csv(csv_file)
            
            if 'hsgt_holding_shares' in df.columns:
                target_date_hsgt = df[(df['time_key'].str.contains(target_date)) & (df['hsgt_holding_shares'].notna())]
                
                if len(target_date_hsgt) > 0:
                    hsgt_info = target_date_hsgt.iloc[0]
                    stocks_with_target_date.append({
                        'stock_code': stock_code,
                        'shares': hsgt_info['hsgt_holding_shares'],
                        'ratio': hsgt_info['hsgt_holding_ratio']
                    })
                else:
                    stocks_without_target_date.append(stock_code)
                    
        except Exception as e:
            continue
    
    print(f"\n📊 {target_date} 港股通数据统计 (前50只股票):")
    print(f"有 {target_date} 港股通数据: {len(stocks_with_target_date)} 只")
    print(f"无 {target_date} 港股通数据: {len(stocks_without_target_date)} 只")
    
    if stocks_with_target_date:
        print(f"\n有 {target_date} 数据的股票示例:")
        for stock in stocks_with_target_date[:10]:
            print(f"  {stock['stock_code']}: {stock['shares']:,.0f} 股 ({stock['ratio']}%)")
    
    if stocks_without_target_date:
        print(f"\n无 {target_date} 数据的股票: {stocks_without_target_date[:10]}")

if __name__ == "__main__":
    check_and_fix_hsgt_data()
    check_specific_date_coverage('2025-07-28')
