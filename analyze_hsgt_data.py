#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析腾讯控股(00700)的港股通持仓数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

def load_and_analyze_data():
    """
    加载并分析港股通数据
    """
    print("=" * 60)
    print("腾讯控股(00700)港股通持仓数据分析")
    print("=" * 60)
    
    # 读取数据
    df = pd.read_csv('data/H_daily/00700.csv')
    print(f"总数据记录数: {len(df)}")
    
    # 筛选有港股通数据的记录
    hsgt_data = df[df['hsgt_holding_shares'].notna()].copy()
    print(f"包含港股通数据的记录数: {len(hsgt_data)}")
    
    if len(hsgt_data) == 0:
        print("没有港股通数据可供分析")
        return
    
    # 转换日期格式
    hsgt_data['date'] = pd.to_datetime(hsgt_data['time_key'])
    hsgt_data = hsgt_data.sort_values('date')
    
    print(f"港股通数据时间范围: {hsgt_data['date'].min().strftime('%Y-%m-%d')} 到 {hsgt_data['date'].max().strftime('%Y-%m-%d')}")
    
    # 基本统计信息
    print("\n" + "-" * 40)
    print("📊 港股通持仓基本统计:")
    print("-" * 40)
    
    latest = hsgt_data.iloc[-1]
    earliest = hsgt_data.iloc[0]
    
    print(f"最新持股数量: {latest['hsgt_holding_shares']:,.0f} 股")
    print(f"最新持股市值: {latest['hsgt_holding_value']:,.0f} 港元")
    print(f"最新持股比例: {latest['hsgt_holding_ratio']:.2f}%")
    print(f"最新股价: {latest['close']:.2f} 港元")
    
    # 持股变化分析
    print(f"\n初始持股数量: {earliest['hsgt_holding_shares']:,.0f} 股")
    print(f"初始持股市值: {earliest['hsgt_holding_value']:,.0f} 港元")
    print(f"初始持股比例: {earliest['hsgt_holding_ratio']:.2f}%")
    
    shares_change = latest['hsgt_holding_shares'] - earliest['hsgt_holding_shares']
    shares_change_pct = (shares_change / earliest['hsgt_holding_shares']) * 100
    
    print(f"\n持股数量变化: {shares_change:,.0f} 股 ({shares_change_pct:+.2f}%)")
    
    # 统计分析
    print("\n" + "-" * 40)
    print("📈 持股数量统计分析:")
    print("-" * 40)
    
    shares_stats = hsgt_data['hsgt_holding_shares'].describe()
    print(f"平均持股: {shares_stats['mean']:,.0f} 股")
    print(f"最大持股: {shares_stats['max']:,.0f} 股")
    print(f"最小持股: {shares_stats['min']:,.0f} 股")
    print(f"标准差: {shares_stats['std']:,.0f} 股")
    
    # 持股比例分析
    print("\n" + "-" * 40)
    print("📊 持股比例统计分析:")
    print("-" * 40)
    
    ratio_stats = hsgt_data['hsgt_holding_ratio'].describe()
    print(f"平均持股比例: {ratio_stats['mean']:.2f}%")
    print(f"最大持股比例: {ratio_stats['max']:.2f}%")
    print(f"最小持股比例: {ratio_stats['min']:.2f}%")
    print(f"标准差: {ratio_stats['std']:.2f}%")
    
    # 月度变化分析
    print("\n" + "-" * 40)
    print("📅 月度持股变化分析:")
    print("-" * 40)
    
    hsgt_data['year_month'] = hsgt_data['date'].dt.to_period('M')
    monthly_data = hsgt_data.groupby('year_month').agg({
        'hsgt_holding_shares': 'last',
        'hsgt_holding_value': 'last',
        'hsgt_holding_ratio': 'last',
        'close': 'last'
    }).reset_index()
    
    # 计算月度变化
    monthly_data['shares_change'] = monthly_data['hsgt_holding_shares'].diff()
    monthly_data['shares_change_pct'] = monthly_data['hsgt_holding_shares'].pct_change() * 100
    
    print("最近6个月的持股变化:")
    recent_months = monthly_data.tail(6)
    for _, row in recent_months.iterrows():
        change_str = ""
        if pd.notna(row['shares_change']):
            change_str = f" (变化: {row['shares_change']:+,.0f} 股, {row['shares_change_pct']:+.2f}%)"
        print(f"{row['year_month']}: {row['hsgt_holding_shares']:,.0f} 股, {row['hsgt_holding_ratio']:.2f}%{change_str}")
    
    # 价格与持股关系分析
    print("\n" + "-" * 40)
    print("💰 股价与持股关系分析:")
    print("-" * 40)
    
    correlation = hsgt_data['close'].corr(hsgt_data['hsgt_holding_shares'])
    print(f"股价与持股数量相关系数: {correlation:.4f}")
    
    if abs(correlation) > 0.5:
        relationship = "强相关" if abs(correlation) > 0.7 else "中等相关"
        direction = "正相关" if correlation > 0 else "负相关"
        print(f"关系强度: {relationship} ({direction})")
    else:
        print("关系强度: 弱相关")
    
    # 近期趋势分析
    print("\n" + "-" * 40)
    print("📈 近期趋势分析 (最近30个交易日):")
    print("-" * 40)
    
    recent_30 = hsgt_data.tail(30)
    if len(recent_30) >= 2:
        recent_shares_change = recent_30.iloc[-1]['hsgt_holding_shares'] - recent_30.iloc[0]['hsgt_holding_shares']
        recent_shares_change_pct = (recent_shares_change / recent_30.iloc[0]['hsgt_holding_shares']) * 100
        
        recent_price_change = recent_30.iloc[-1]['close'] - recent_30.iloc[0]['close']
        recent_price_change_pct = (recent_price_change / recent_30.iloc[0]['close']) * 100
        
        print(f"持股数量变化: {recent_shares_change:+,.0f} 股 ({recent_shares_change_pct:+.2f}%)")
        print(f"股价变化: {recent_price_change:+.2f} 港元 ({recent_price_change_pct:+.2f}%)")
        
        if recent_shares_change > 0:
            trend = "增持"
        elif recent_shares_change < 0:
            trend = "减持"
        else:
            trend = "持平"
        print(f"近期趋势: {trend}")
    
    # 保存分析结果摘要
    print("\n" + "-" * 40)
    print("💾 保存分析结果...")
    print("-" * 40)
    
    summary = {
        '分析日期': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        '数据时间范围': f"{hsgt_data['date'].min().strftime('%Y-%m-%d')} 到 {hsgt_data['date'].max().strftime('%Y-%m-%d')}",
        '总记录数': len(hsgt_data),
        '最新持股数量': latest['hsgt_holding_shares'],
        '最新持股市值': latest['hsgt_holding_value'],
        '最新持股比例': latest['hsgt_holding_ratio'],
        '最新股价': latest['close'],
        '平均持股数量': shares_stats['mean'],
        '平均持股比例': ratio_stats['mean'],
        '股价持股相关系数': correlation
    }
    
    summary_df = pd.DataFrame([summary])
    summary_df.to_csv('hsgt_analysis_summary.csv', index=False, encoding='utf-8')
    print("分析摘要已保存到: hsgt_analysis_summary.csv")
    
    print("\n" + "=" * 60)
    print("✅ 分析完成！")
    print("=" * 60)

if __name__ == "__main__":
    load_and_analyze_data()
