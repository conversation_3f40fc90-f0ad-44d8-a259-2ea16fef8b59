#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化分析工具

专门用于分析港股通持股比例的变化情况，找出持股比例增加最大的公司。
"""

import sys
import os
import pandas as pd
from datetime import datetime

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def analyze_hsgt_holdings_change(df: pd.DataFrame, days: int = 5) -> pd.DataFrame:
    """
    分析港股通持股比例变化，找出最近N日增加最大的公司
    
    Args:
        df: 港股数据DataFrame
        days: 分析的天数，默认5天
        
    Returns:
        DataFrame: 包含持股比例变化分析结果的数据框
    """
    print(f"分析最近{days}日港股通持股比例变化...")
    
    # 过滤有港股通数据的记录
    hsgt_data = df[df['hsgt_holding_ratio'].notna()].copy()
    
    if hsgt_data.empty:
        print("未找到港股通持股数据")
        return pd.DataFrame()
    
    # 按股票代码分组，计算每只股票最近N日的持股比例变化
    latest_date = hsgt_data['time_key'].max()
    start_date = latest_date - pd.Timedelta(days=days)
    
    print(f"分析时间范围: {start_date.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')}")
    
    recent_data = hsgt_data[hsgt_data['time_key'] >= start_date].copy()
    
    results = []
    for code in recent_data['code'].unique():
        stock_data = recent_data[recent_data['code'] == code].sort_values('time_key')
        
        if len(stock_data) < 2:
            continue
            
        # 计算持股比例变化
        latest_ratio = stock_data['hsgt_holding_ratio'].iloc[-1]
        earliest_ratio = stock_data['hsgt_holding_ratio'].iloc[0]
        
        if pd.notna(latest_ratio) and pd.notna(earliest_ratio):
            ratio_change = latest_ratio - earliest_ratio
            
            results.append({
                'code': code,
                'name': stock_data['name'].iloc[-1],
                'latest_date': stock_data['time_key'].iloc[-1].strftime('%Y-%m-%d'),
                'earliest_date': stock_data['time_key'].iloc[0].strftime('%Y-%m-%d'),
                'earliest_ratio': earliest_ratio,
                'latest_ratio': latest_ratio,
                'ratio_change': ratio_change,
                'ratio_change_pct': (ratio_change / earliest_ratio * 100) if earliest_ratio > 0 else 0,
                'latest_holding_value': stock_data['hsgt_holding_value'].iloc[-1] if 'hsgt_holding_value' in stock_data.columns else None,
                'latest_close': stock_data['close'].iloc[-1],
                'data_points': len(stock_data)
            })
    
    if not results:
        print("未找到有效的港股通持股比例变化数据")
        return pd.DataFrame()
    
    # 转换为DataFrame并按持股比例变化排序
    result_df = pd.DataFrame(results)
    result_df = result_df.sort_values('ratio_change', ascending=False)
    
    return result_df

def main():
    """主函数"""
    print("="*80)
    print("           港股通持股比例变化分析工具")
    print("="*80)
    
    # 数据文件路径
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    
    if not os.path.exists(data_path):
        print(f"错误: 未找到数据文件 {data_path}")
        return
    
    print(f"正在加载数据: {data_path}")
    try:
        df = pd.read_parquet(data_path)
        print(f"数据加载成功，共 {len(df)} 条记录")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 分析港股通持股比例变化
    analysis_days = 5  # 可以根据需要调整
    hsgt_analysis = analyze_hsgt_holdings_change(df, days=analysis_days)
    
    if hsgt_analysis.empty:
        print("未找到港股通持股比例变化数据")
        return
    
    # 显示结果 - 持股比例增加最多的
    print(f"\n🔥 港股通持股比例增加最大的10家公司 (最近{analysis_days}日):")
    print("="*120)

    # 显示前10名
    top_10_increase = hsgt_analysis.head(10)

    # 格式化显示
    display_cols = ['code', 'name', 'latest_date', 'earliest_ratio', 'latest_ratio',
                   'ratio_change', 'ratio_change_pct', 'latest_close']

    formatted_increase = top_10_increase[display_cols].copy()
    formatted_increase['earliest_ratio'] = formatted_increase['earliest_ratio'].round(2)
    formatted_increase['latest_ratio'] = formatted_increase['latest_ratio'].round(2)
    formatted_increase['ratio_change'] = formatted_increase['ratio_change'].round(2)
    formatted_increase['ratio_change_pct'] = formatted_increase['ratio_change_pct'].round(2)
    formatted_increase['latest_close'] = formatted_increase['latest_close'].round(2)

    # 重命名列名为中文
    formatted_increase.columns = ['股票代码', '股票名称', '最新日期', '期初持股比例(%)',
                                 '最新持股比例(%)', '持股比例变化', '变化百分比(%)', '最新收盘价']

    print(formatted_increase.to_string(index=False))

    # 显示结果 - 持股比例减少最多的
    print(f"\n📉 港股通持股比例减少最大的10家公司 (最近{analysis_days}日):")
    print("="*120)

    # 显示后10名（减少最多的）
    bottom_10_decrease = hsgt_analysis.tail(10).iloc[::-1]  # 取最后10个并反转顺序

    formatted_decrease = bottom_10_decrease[display_cols].copy()
    formatted_decrease['earliest_ratio'] = formatted_decrease['earliest_ratio'].round(2)
    formatted_decrease['latest_ratio'] = formatted_decrease['latest_ratio'].round(2)
    formatted_decrease['ratio_change'] = formatted_decrease['ratio_change'].round(2)
    formatted_decrease['ratio_change_pct'] = formatted_decrease['ratio_change_pct'].round(2)
    formatted_decrease['latest_close'] = formatted_decrease['latest_close'].round(2)

    # 重命名列名为中文
    formatted_decrease.columns = ['股票代码', '股票名称', '最新日期', '期初持股比例(%)',
                                 '最新持股比例(%)', '持股比例变化', '变化百分比(%)', '最新收盘价']

    print(formatted_decrease.to_string(index=False))
    
    # 统计信息
    total_increase_count = len(hsgt_analysis[hsgt_analysis['ratio_change'] > 0])
    total_decrease_count = len(hsgt_analysis[hsgt_analysis['ratio_change'] < 0])
    avg_change = hsgt_analysis['ratio_change'].mean()
    max_increase = hsgt_analysis['ratio_change'].max()
    max_decrease = hsgt_analysis['ratio_change'].min()
    
    print(f"\n📊 统计信息:")
    print(f"   分析公司总数: {len(hsgt_analysis)} 家")
    print(f"   持股比例增加的公司: {total_increase_count} 家")
    print(f"   持股比例减少的公司: {total_decrease_count} 家")
    print(f"   平均持股比例变化: {avg_change:.2f}%")
    print(f"   最大增幅: {max_increase:.2f}%")
    print(f"   最大减幅: {max_decrease:.2f}%")
    
    # 保存结果
    output_dir = os.path.join(os.path.dirname(__file__), 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_filename = f'hsgt_holdings_analysis_{current_date}.csv'
    csv_path = os.path.join(output_dir, csv_filename)
    
    hsgt_analysis.to_csv(csv_path, index=False, encoding='utf-8-sig')
    print(f"\n💾 完整分析结果已保存到: {csv_path}")
    print(f"   (包含所有 {len(hsgt_analysis)} 家公司的详细数据)")
    
    print("="*80)

if __name__ == "__main__":
    main()
