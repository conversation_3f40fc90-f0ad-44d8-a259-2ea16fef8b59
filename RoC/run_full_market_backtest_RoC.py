import sys
import os
import pandas as pd
from tqdm import tqdm
from multiprocessing import Pool, cpu_count
from functools import partial
import json # For saving summary stats

# --- Factor Calculation Enhancement ---
# This script now includes four key factors in the output data:
# 1. RoC(5): 5-day rate of change in percentage
# 2. Max(5): 5-day rolling maximum of daily returns
# 3. Vol(5): 5-day rolling standard deviation of daily returns
# 4. Abn_turnover(5): Abnormal turnover (5-day mean volume / 250-day mean volume)
# These factors are calculated at the entry date for each trade and included in the CSV output.

# --- Strategy Parameters ---
strategy_params = {
    'roc_period': 30,                    # best 30
    'roc_std_window': 500,               # best 750
    'roc_std_multiplier': 1.7,           # best 2.0
    'atr_period': 14,                    # 标准ATR周期
    'profit_target_atr_multiplier': 2, # best 2.0
    'stop_loss_atr_multiplier': 1.5,     # best 1.5
    'max_hold_days': 30                  # best 30
}

# --- Random Benchmark Parameters ---
enable_random_benchmark = True          # 是否启用随机基准对比
random_benchmark_sample_ratio = 0.1     # 随机基准测试的股票采样比例（10%）
random_seed = 42                        # 随机种子，确保结果可重现

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Assuming roc_strategy.py is in strategies/RoC/
from strategies.RoC.roc_strategy import apply_roc_atr_strategy
from utilities.utils import load_stock_name_mapping

# --- Helper Functions for Stock Filtering ---
def get_stock_blacklist(project_root_path):
    """
    Loads all stock codes from stock list CSVs that contain 'ST' or '退'
    in their name, all stocks from the delisted stocks file, and all stocks
    from the blacklist.txt file. This is used to build a blacklist.
    """
    blacklist_stocks = set()

    # Files with stocks to be filtered by name for ST or 退
    list_files = ['sh_stock_list.csv', 'sz_stock_list.csv', 'bj_stock_list.csv']
    for file_name in list_files:
        try:
            path = os.path.join(project_root_path, 'ashare', file_name)
            df = pd.read_csv(path, dtype={'证券代码': str})
            # Filter for names containing 'ST' OR '退' using regex
            filtered_df = df[df['证券简称'].str.contains('ST|退', na=False, regex=True)]
            blacklist_stocks.update(filtered_df['证券代码'].str.zfill(6))
        except FileNotFoundError:
            print(f"Warning: Stock list file not found at {path}")
        except Exception as e:
            print(f"An error occurred while processing {file_name}: {e}")

    # Add all stocks from the delisted file
    try:
        delisted_path = os.path.join(project_root_path, 'ashare', 'delisted_stock_list.csv')
        delisted_df = pd.read_csv(delisted_path, dtype={'证券代码': str})
        # The column name is '证券代码'
        # Drop rows where '证券代码' is NaN, which can happen with empty lines in CSV
        delisted_df.dropna(subset=['证券代码'], inplace=True)
        blacklist_stocks.update(delisted_df['证券代码'].str.zfill(6))
    except FileNotFoundError:
        print(f"Warning: Delisted stock file not found at {delisted_path}")
    except Exception as e:
        print(f"An error occurred while processing delisted_stock_list.csv: {e}")

    # Add all stocks from the blacklist.txt file
    try:
        blacklist_path = os.path.join(project_root_path, 'ashare', 'blacklist.txt')
        if os.path.exists(blacklist_path):
            with open(blacklist_path, 'r', encoding='utf-8') as f:
                # Read each line, strip whitespace, and ignore empty lines
                blacklist_codes = {line.strip() for line in f if line.strip()}
                # Ensure 6-digit format
                blacklist_codes = {code.zfill(6) for code in blacklist_codes}
                blacklist_stocks.update(blacklist_codes)
                print(f"Loaded {len(blacklist_codes)} stocks from blacklist.txt")
        else:
            print("Info: blacklist.txt file not found, skipping custom blacklist")
    except Exception as e:
        print(f"An error occurred while processing blacklist.txt: {e}")

    return blacklist_stocks

# 导入内存优化工具
try:
    from utilities.performance_optimizations import (
        optimize_dataframe_memory,
        PerformanceProfiler
    )
    MEMORY_OPTIMIZATION_AVAILABLE = True
    print("内存优化模块已加载")
except ImportError:
    print("内存优化模块不可用，使用标准模式")
    MEMORY_OPTIMIZATION_AVAILABLE = False

    # 提供备用函数
    def optimize_dataframe_memory(df, preserve_price_precision=True):
        return df

    class PerformanceProfiler:
        def profile_function(self, name):
            def decorator(func):
                return func
            return decorator

def add_stock_code_prefix(df):
    """Adds 'sh.', 'sz.', or 'bj.' prefix to stock codes based on their first digits."""
    
    def get_prefix(code):
        if isinstance(code, str):
            if code.startswith('60') or code.startswith('688'):
                return f"sh.{code}"
            elif code.startswith('00') or code.startswith('30'):
                return f"sz.{code}"
            elif code.startswith('4') or code.startswith('8') or code.startswith('9'):
                return f"bj.{code}"
        return code # Return original if not a string or no match

    # Ensure StockCode is string type before applying string operations
    df['StockCode'] = df['StockCode'].astype(str).str.zfill(6)
    df['StockCode'] = df['StockCode'].apply(get_prefix)
    return df

def calculate_factors(group_df):
    """
    Calculate the four factors (ROC, MAX, ABN, VOL) for a stock's data.
    Returns the group_df with added factor columns.
    """
    if len(group_df) < 10:  # Not enough data
        return group_df

    # Calculate daily returns first
    group_df['Returns'] = group_df['Close'].pct_change()

    # ROC(5): 5-day rate of change in percentage
    group_df['RoC(5)'] = ((group_df['Close'] - group_df['Close'].shift(5)) / group_df['Close'].shift(5)) * 100

    # MAX(5): 5-day rolling maximum of daily returns
    group_df['Max(5)'] = group_df['Returns'].rolling(window=5, min_periods=1).max()

    # VOL(5): 5-day rolling standard deviation of daily returns
    group_df['Vol(5)'] = group_df['Returns'].rolling(window=5, min_periods=1).std()

    # ABN(5): Abnormal turnover - 5-day mean volume / 250-day mean volume
    if 'Volume' in group_df.columns and not group_df['Volume'].isna().all():
        group_df['Abn_turnover(5)'] = (
            group_df['Volume'].rolling(window=5, min_periods=1).mean() /
            group_df['Volume'].rolling(window=250, min_periods=50).mean()
        )
    else:
        # If no volume data, use default value
        group_df['Abn_turnover(5)'] = 1.0

    return group_df

def process_stock(stock_data, params):
    """
    Wrapper function to apply the strategy to a single stock's data.
    `stock_data` is a tuple (stock_code, group_df).
    `params` is the dictionary of strategy parameters.
    """
    stock_code, group_df = stock_data
    group_df = group_df.sort_index()

    # Calculate factors first
    group_df = calculate_factors(group_df)

    try:
        _, trades = apply_roc_atr_strategy(group_df, **params)
        if trades:
            for trade in trades:
                trade['StockCode'] = stock_code

                # Add factor data at entry date
                entry_date = pd.to_datetime(trade['EntryDate'])
                if entry_date in group_df.index:
                    trade['RoC(5)'] = group_df.loc[entry_date, 'RoC(5)']
                    trade['Max(5)'] = group_df.loc[entry_date, 'Max(5)']
                    trade['Vol(5)'] = group_df.loc[entry_date, 'Vol(5)']
                    trade['Abn_turnover(5)'] = group_df.loc[entry_date, 'Abn_turnover(5)']
                else:
                    # If exact date not found, use NaN
                    trade['RoC(5)'] = pd.NA
                    trade['Max(5)'] = pd.NA
                    trade['Vol(5)'] = pd.NA
                    trade['Abn_turnover(5)'] = pd.NA

            return trades
    except Exception as e:
        # print(f"Could not process {stock_code}: {e}")
        return None
    return None

def process_stock_random_benchmark(stock_data, strategy_trades_count, params):
    """
    Apply random entry/exit strategy to a single stock for benchmark comparison.

    Args:
        stock_data: tuple (stock_code, group_df)
        strategy_trades_count: number of trades the real strategy generated for this stock
        params: parameters including random seed

    Returns:
        list: Random trades for this stock
    """
    import numpy as np

    stock_code, group_df = stock_data
    group_df = group_df.sort_index()

    if strategy_trades_count <= 0 or len(group_df) < 30:
        return None

    try:
        # Calculate factors for random benchmark as well
        group_df = calculate_factors(group_df)

        # Set random seed for reproducibility
        np.random.seed(params.get('random_seed', 42))

        # Generate random entry dates
        max_hold_days = params.get('max_hold_days', 30)
        available_dates = group_df.index[:-max_hold_days]

        if len(available_dates) < strategy_trades_count:
            num_trades = len(available_dates)
        else:
            num_trades = strategy_trades_count

        if num_trades <= 0:
            return None

        random_entry_dates = np.random.choice(available_dates, size=num_trades, replace=False)
        random_entry_dates = sorted(random_entry_dates)

        trades_log = []
        for entry_date in random_entry_dates:
            try:
                entry_price = group_df.loc[entry_date, 'Close']

                # Random holding period (1 to max_hold_days)
                hold_days = np.random.randint(1, max_hold_days + 1)

                # Find exit date
                entry_idx = group_df.index.get_loc(entry_date)
                if entry_idx + hold_days < len(group_df):
                    exit_date = group_df.index[entry_idx + hold_days]
                else:
                    exit_date = group_df.index[-1]

                exit_price = group_df.loc[exit_date, 'Close']

                pnl_absolute = exit_price - entry_price
                pnl_percent = (pnl_absolute / entry_price) * 100 if entry_price != 0 else 0

                trade_record = {
                    'StockCode': stock_code,
                    'EntryDate': entry_date,
                    'ExitDate': exit_date,
                    'EntryPrice': entry_price,
                    'ExitPrice': exit_price,
                    'PnL_absolute': pnl_absolute,
                    'PnL_pct': pnl_percent,
                    'HoldingDays': hold_days,
                    'ExitReason': 'Random Exit'
                }

                # Add factor data at entry date
                if entry_date in group_df.index:
                    trade_record['RoC(5)'] = group_df.loc[entry_date, 'RoC(5)']
                    trade_record['Max(5)'] = group_df.loc[entry_date, 'Max(5)']
                    trade_record['Vol(5)'] = group_df.loc[entry_date, 'Vol(5)']
                    trade_record['Abn_turnover(5)'] = group_df.loc[entry_date, 'Abn_turnover(5)']
                else:
                    # If exact date not found, use NaN
                    trade_record['RoC(5)'] = pd.NA
                    trade_record['Max(5)'] = pd.NA
                    trade_record['Vol(5)'] = pd.NA
                    trade_record['Abn_turnover(5)'] = pd.NA

                trades_log.append(trade_record)

            except Exception:
                continue

        return trades_log if trades_log else None

    except Exception as e:
        return None

def calculate_summary_statistics(trades_df):
    """Calculates and returns a dictionary of summary statistics from the trades DataFrame."""
    if trades_df.empty:
        return {
            "Total Trades": 0,
            "Message": "No trades generated to calculate statistics."
        }

    # Calculate PnL based on 10,000 RMB investment per trade (consistent with roc_strategy.py)
    investment_amount = 10000  # 1万元
    trades_df = trades_df.copy()  # Avoid modifying original DataFrame
    trades_df['Shares_Bought'] = investment_amount / trades_df['EntryPrice']
    trades_df['PnL_10k'] = trades_df['Shares_Bought'] * trades_df['PnL_absolute']

    stats = {}
    stats["Total Trades"] = len(trades_df)

    # Use percentage-based classification for winning/losing trades
    winning_trades_df = trades_df[trades_df['PnL_pct'] > 0]
    losing_trades_df = trades_df[trades_df['PnL_pct'] <= 0]

    stats["Winning Trades"] = len(winning_trades_df)
    stats["Losing Trades"] = len(losing_trades_df)

    stats["Win Rate (%)"] = (stats["Winning Trades"] / stats["Total Trades"]) * 100 if stats["Total Trades"] > 0 else 0

    # Percentage-based statistics
    stats["Average PnL per Trade (%)"] = trades_df['PnL_pct'].mean()

    # 1万元投资-based statistics (consistent with roc_strategy.py)
    stats["Total PnL (1万元/交易)"] = trades_df['PnL_10k'].sum()
    stats["Average PnL per Trade (¥)"] = trades_df['PnL_10k'].mean()
    stats["Gross Profit (¥)"] = winning_trades_df['PnL_10k'].sum()
    stats["Gross Loss (¥)"] = abs(losing_trades_df['PnL_10k'].sum())

    # Profit factor calculation using 1万元 investment
    if stats["Gross Loss (¥)"] > 0:
        stats["Profit Factor"] = stats["Gross Profit (¥)"] / stats["Gross Loss (¥)"]
    else:
        stats["Profit Factor"] = float('inf') if stats["Gross Profit (¥)"] > 0 else 0
        
    stats["Average Holding Days"] = trades_df['HoldingDays'].mean()
    
    exit_reason_counts = trades_df['ExitReason'].value_counts().to_dict()
    exit_reason_pct = trades_df['ExitReason'].value_counts(normalize=True).mul(100).to_dict()
    
    stats["Exit Reason Counts"] = exit_reason_counts
    stats["Exit Reason Percentages Raw"] = exit_reason_pct

    return stats

def calculate_additional_factors(trades_df, market_data_df):
    """
    Calculate additional factors for analysis: RoC(5), Max(5), Vol(5), Abn_turnover(5)
    """
    import numpy as np

    # Ensure dates are datetime
    trades_df['EntryDate'] = pd.to_datetime(trades_df['EntryDate'])
    market_data_df = market_data_df.reset_index()
    market_data_df['Date'] = pd.to_datetime(market_data_df['Date'])

    # Ensure StockCode format consistency - add prefixes to market data if needed
    def add_stock_code_prefix(df):
        """Adds 'sh.' or 'sz.' prefix to stock codes based on their first digits."""

        def get_prefix(code):
            if isinstance(code, str):
                if code.startswith('60') or code.startswith('688'):
                    return f"sh.{code}"
                elif code.startswith('00') or code.startswith('300'):
                    return f"sz.{code}"
                elif code.startswith('8') or code.startswith('43'):
                    return f"bj.{code}"
            return code # Return original if not a string or no match

        # Ensure StockCode is string type before applying string operations
        if 'StockCode' in df.columns:
            df['StockCode'] = df['StockCode'].astype(str).str.zfill(6)
            df['StockCode'] = df['StockCode'].apply(get_prefix)
        return df

    # Apply prefix to market data
    market_data_df = add_stock_code_prefix(market_data_df)

    # Calculate daily returns
    market_data_df = market_data_df.sort_values(['StockCode', 'Date'])
    market_data_df['Returns'] = market_data_df.groupby('StockCode')['Close'].pct_change()

    # Set multi-index for efficient operations
    market_data_indexed = market_data_df.set_index(['StockCode', 'Date']).sort_index()

    # Calculate factors
    print("  Calculating RoC(5) - 5-day rate of change...")
    market_data_indexed['RoC(5)'] = market_data_indexed.groupby(level='StockCode')['Close'].transform(
        lambda x: ((x - x.shift(5)) / x.shift(5)) * 100
    )

    print("  Calculating Max(5) - 5-day maximum return...")
    market_data_indexed['Max(5)'] = market_data_indexed.groupby(level='StockCode')['Returns'].transform(
        lambda x: x.rolling(window=5, min_periods=1).max()
    )

    print("  Calculating Vol(5) - 5-day volatility...")
    market_data_indexed['Vol(5)'] = market_data_indexed.groupby(level='StockCode')['Returns'].transform(
        lambda x: x.rolling(window=5, min_periods=1).std()
    )

    print("  Calculating Abn_turnover(5) - abnormal turnover...")
    if 'Volume' in market_data_indexed.columns and not market_data_indexed['Volume'].isna().all():
        market_data_indexed['Abn_turnover(5)'] = market_data_indexed.groupby(level='StockCode')['Volume'].transform(
            lambda x: x.rolling(window=5, min_periods=1).mean() / x.rolling(window=20, min_periods=5).mean()
        )
    else:
        market_data_indexed['Abn_turnover(5)'] = 1.0

    # Merge factors with trades
    print("  Merging factors with trade data...")
    factor_columns = ['RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']

    # Debug: Check data formats before merge
    print(f"  Debug: Sample trade StockCodes: {trades_df['StockCode'].head(3).tolist()}")
    print(f"  Debug: Sample market data StockCodes: {market_data_indexed.index.get_level_values('StockCode').unique()[:3].tolist()}")
    print(f"  Debug: Sample trade dates: {trades_df['EntryDate'].head(3).tolist()}")
    print(f"  Debug: Sample market data dates: {market_data_indexed.index.get_level_values('Date').unique()[:3].tolist()}")

    # Remove existing factor columns from trades_df to avoid conflicts
    trades_df_clean = trades_df.copy()
    for col in factor_columns:
        if col in trades_df_clean.columns:
            trades_df_clean = trades_df_clean.drop(columns=[col])

    trades_with_factors = pd.merge(
        trades_df_clean,
        market_data_indexed[factor_columns],
        left_on=['StockCode', 'EntryDate'],
        right_index=True,
        how='left'
    )

    # Debug: Check merge results
    print(f"  Debug: Merged data columns: {list(trades_with_factors.columns)}")
    print(f"  Debug: Merged data shape: {trades_with_factors.shape}")

    # Check if factor columns exist in merged data
    existing_factor_columns = [col for col in factor_columns if col in trades_with_factors.columns]
    print(f"  Debug: Existing factor columns: {existing_factor_columns}")

    if existing_factor_columns:
        # Check merge success
        factor_success_count = trades_with_factors[existing_factor_columns].notna().all(axis=1).sum()
        print(f"  Successfully calculated factors for {factor_success_count}/{len(trades_with_factors)} trades")

        # Debug: Show sample of merged data
        print(f"  Debug: Sample merged data:")
        sample_cols = ['StockCode', 'EntryDate'] + existing_factor_columns
        available_cols = [col for col in sample_cols if col in trades_with_factors.columns]
        sample_data = trades_with_factors[available_cols].head(3)
        print(sample_data)
    else:
        print("  Warning: No factor columns found in merged data")
        # Add empty factor columns to maintain structure
        for col in factor_columns:
            trades_with_factors[col] = pd.NA
        print(f"  Added empty factor columns: {factor_columns}")

    return trades_with_factors

def print_summary_statistics(stats, strategy_params=None, random_stats=None):
    """Prints the summary statistics and strategy parameters in a readable format."""
    print("\n--- Strategy Parameters ---")
    if strategy_params:
        for key, value in strategy_params.items():
            print(f"{key}: {value}")
    else:
        print("No strategy parameters provided.")
    print("---------------------------")
    
    print("\n--- Backtest Summary Statistics ---")
    if "Message" in stats:
        print(stats["Message"])
        print("-----------------------------------")
        return

    for key, value in stats.items():
        if key == "Exit Reason Percentages Raw":
            print("Exit Reason Percentages:")
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    print(f"  - {sub_key}: {sub_value:.2f}%")
            else:
                print(f"  {value}")
        elif isinstance(value, float):
            print(f"{key}: {value:,.2f}")
        elif isinstance(value, dict): # For Exit Reason Counts
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  - {sub_key}: {sub_value}")
        else: # Integers, strings
            print(f"{key}: {value}")
    print("-----------------------------------")

    # Print random benchmark comparison if available
    if random_stats:
        print("\n🎲 策略 vs 随机基准对比:")
        print("=" * 50)

        strategy_total_pnl = stats.get("Total PnL (1万元/交易)", 0)
        random_total_pnl = random_stats.get("Total PnL (1万元/交易)", 0)
        strategy_win_rate = stats.get("Win Rate (%)", 0)
        random_win_rate = random_stats.get("Win Rate (%)", 0)
        strategy_trades = stats.get("Total Trades", 0)
        random_trades = random_stats.get("Total Trades", 0)

        print(f"策略总收益: ¥{strategy_total_pnl:,.2f}")
        print(f"随机总收益: ¥{random_total_pnl:,.2f}")
        print(f"策略优势: ¥{strategy_total_pnl - random_total_pnl:,.2f}")
        print(f"策略胜率: {strategy_win_rate:.2f}% vs 随机胜率: {random_win_rate:.2f}%")
        print(f"策略交易数: {strategy_trades} vs 随机交易数: {random_trades}")

        # Calculate alpha and performance ratio
        if random_total_pnl != 0:
            performance_ratio = strategy_total_pnl / random_total_pnl
            print(f"表现倍数: {performance_ratio:.2f}x (策略收益/随机收益)")

        if strategy_trades > 0:
            alpha_pct = ((strategy_total_pnl - random_total_pnl) / (strategy_trades * 10000)) * 100
            print(f"策略Alpha: {alpha_pct:.2f}% (超越随机基准的收益)")

        print("=" * 50)


def run_full_market_backtest(data_path, output_path, strategy_params, start_date=None, end_date=None):
    """
    Runs the backtest on all stocks in the provided Parquet file using multiprocessing.
    Saves trades to CSV and prints/saves summary statistics including strategy parameters.
    """
    print("Loading stock name mapping...")
    project_root_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    stock_name_map = load_stock_name_mapping(project_root_path)
    if not stock_name_map:
        print("Warning: Stock name mapping is empty. 'StockName' column will be empty.")

    print("Loading full market data from Parquet file...")
    df_all = pd.read_parquet(data_path)

    print(f"Loaded {len(df_all)} rows of data.")

    # 应用内存优化（保护价格数据精度）
    if MEMORY_OPTIMIZATION_AVAILABLE:
        print("应用内存优化（保护价格数据精度）...")
        original_memory = df_all.memory_usage(deep=True).sum() / 1024**2
        df_all = optimize_dataframe_memory(df_all, preserve_price_precision=True)
        optimized_memory = df_all.memory_usage(deep=True).sum() / 1024**2
        print(f"内存优化完成: {original_memory:.1f}MB -> {optimized_memory:.1f}MB")

    df_all.rename(columns={'日期': 'Date', '股票代码': 'StockCode', '开盘': 'Open', '最高': 'High', '最低': 'Low', '收盘': 'Close', '成交量': 'Volume'}, inplace=True)

    print("Adding stock code prefixes (e.g., 'sh.', 'sz.')...")
    df_all = add_stock_code_prefix(df_all)

    df_all['Date'] = pd.to_datetime(df_all['Date'])
    df_all.set_index('Date', inplace=True)

    # Filter data by date range if provided
    if start_date:
        df_all = df_all[df_all.index >= start_date]
    if end_date:
        df_all = df_all[df_all.index <= end_date]

    # Filter out Beijing Stock Exchange (BSE) stocks
    print("Filtering out Beijing Stock Exchange (BSE) stocks...")
    before_bse_filter = df_all['StockCode'].nunique()
    df_all = df_all[~df_all['StockCode'].str.startswith('bj.')]
    after_bse_filter = df_all['StockCode'].nunique()
    print(f"Removed {before_bse_filter - after_bse_filter} BSE stocks. Remaining: {after_bse_filter} stocks.")

    # Filter out ST, '退', delisted stocks, and custom blacklist
    print("Loading ST, '退', delisted stocks, and custom blacklist...")
    blacklist_codes_no_prefix = get_stock_blacklist(project_root_path)

    # Convert blacklist to have prefixes for comparison
    blacklist_df = pd.DataFrame(list(blacklist_codes_no_prefix), columns=['StockCode'])
    blacklist_df = add_stock_code_prefix(blacklist_df)
    blacklist = set(blacklist_df['StockCode'].unique())
    print(f"Found {len(blacklist)} stocks in the blacklist (ST, '退', delisted, custom).")

    # Filter out blacklisted stocks
    before_blacklist_filter = df_all['StockCode'].nunique()
    df_all = df_all[~df_all['StockCode'].isin(blacklist)]
    after_blacklist_filter = df_all['StockCode'].nunique()
    print(f"Removed {before_blacklist_filter - after_blacklist_filter} blacklisted stocks. Remaining: {after_blacklist_filter} stocks.")

    grouped = df_all.groupby('StockCode')
    tasks = list(grouped)
    
    print(f"\nStarting backtest on {len(tasks)} stocks using {cpu_count()} CPU cores...")
    # Moved strategy_params print to print_summary_statistics for consolidated output
    
    process_func = partial(process_stock, params=strategy_params)
    
    all_trades = []
    with Pool(cpu_count()) as p:
        results = list(tqdm(p.imap(process_func, tasks), total=len(tasks), desc="Backtesting Stocks"))

    for res in results:
        if res:
            all_trades.extend(res)
            
    print(f"\nBacktest complete. Found {len(all_trades)} trades in total.")

    # Random benchmark testing (if enabled)
    random_trades = []
    if enable_random_benchmark and all_trades:
        print(f"\n🎲 Starting random benchmark testing...")
        print(f"Sampling {random_benchmark_sample_ratio*100:.0f}% of stocks for random benchmark...")

        # Create a mapping of stock codes to their trade counts
        trades_df_temp = pd.DataFrame(all_trades)
        stock_trade_counts = trades_df_temp['StockCode'].value_counts().to_dict()

        # Sample stocks for random benchmark
        import random
        random.seed(random_seed)
        sampled_stocks = random.sample(list(stock_trade_counts.keys()),
                                     int(len(stock_trade_counts) * random_benchmark_sample_ratio))

        # Filter tasks to only include sampled stocks
        sampled_tasks = [(stock_code, group_df) for stock_code, group_df in tasks
                        if stock_code in sampled_stocks]

        print(f"Running random benchmark on {len(sampled_tasks)} stocks...")

        # Create random benchmark parameters
        random_params = strategy_params.copy()
        random_params['random_seed'] = random_seed

        # Process random benchmark
        random_results = []
        with Pool(cpu_count()) as p:
            # Create tasks with trade counts
            random_tasks = [(stock_data, stock_trade_counts[stock_data[0]], random_params)
                           for stock_data in sampled_tasks]

            random_results = list(tqdm(
                p.starmap(process_stock_random_benchmark, random_tasks),
                total=len(random_tasks),
                desc="Random Benchmark"
            ))

        # Collect random trades
        for res in random_results:
            if res:
                random_trades.extend(res)

        print(f"Random benchmark complete. Found {len(random_trades)} random trades.")

    # Prepare overall summary dictionary
    full_summary_data = {"Strategy Parameters": strategy_params}


    if all_trades:
        trades_df = pd.DataFrame(all_trades)

        # Add StockName column
        trades_df['StockName'] = trades_df['StockCode'].map(stock_name_map)

        # Calculate additional factors for analysis
        print("Calculating additional factors for analysis...")
        trades_df = calculate_additional_factors(trades_df, df_all)

        cols_order = ['StockCode', 'StockName', 'EntryDate', 'ExitDate', 'EntryPrice', 'ExitPrice', 'PnL_absolute', 'PnL_pct', 'HoldingDays', 'ExitReason', 'ATR_at_Entry', 'ProfitTarget', 'StopLoss', 'RoC(5)', 'Max(5)', 'Vol(5)', 'Abn_turnover(5)']
        for col in cols_order:
            if col not in trades_df.columns:
                trades_df[col] = pd.NA
        trades_df = trades_df[cols_order]
        
        trades_df.sort_values(by='EntryDate', inplace=True)
        trades_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"All trades saved to {output_path}")

        performance_stats = calculate_summary_statistics(trades_df)

        # Calculate random benchmark statistics if available
        random_stats = None
        if random_trades:
            random_trades_df = pd.DataFrame(random_trades)
            random_stats = calculate_summary_statistics(random_trades_df)

        print_summary_statistics(performance_stats, strategy_params, random_stats)
        
        # Add performance stats to the full summary
        full_summary_data["Performance Statistics"] = performance_stats
        
        # Prepare for JSON: rename "Exit Reason Percentages Raw" to "Exit Reason Percentages"
        if "Performance Statistics" in full_summary_data and \
           "Exit Reason Percentages Raw" in full_summary_data["Performance Statistics"]:
            full_summary_data["Performance Statistics"]["Exit Reason Percentages"] = \
                full_summary_data["Performance Statistics"].pop("Exit Reason Percentages Raw")

    else:
        print("No trades were generated across the entire market.")
        performance_stats = calculate_summary_statistics(pd.DataFrame())
        print_summary_statistics(performance_stats, strategy_params) # Pass params here
        full_summary_data["Performance Statistics"] = performance_stats


    # Save full summary (params + stats) to JSON
    summary_output_path = output_path.replace('.csv', '_summary.json')
    try:
        with open(summary_output_path, 'w') as f:
            json.dump(full_summary_data, f, indent=4)
        print(f"Full summary (parameters and statistics) saved to {summary_output_path}")
    except Exception as e:
        print(f"Error saving full summary to JSON: {e}")

def run_backtest_with_params(strategy_params, start_date=None, end_date=None):
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    market_data_path = os.path.join(project_root, 'ashare', 'all_daily_hfq.parquet')
    
    # Create a string from params for the filename
    param_list = []
    for k, v in strategy_params.items():
        # Abbreviate common keys for shorter filenames if desired
        k_short = k.replace('_period','P').replace('_window','W').replace('_multiplier','M').replace('target','Tgt').replace('loss','SL').replace('days','D')
        param_list.append(f"{k_short}{v}")
    param_str = "_".join(param_list)
    
    output_filename_base = f"full_market_trades_RoC"
    output_csv_path = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results', f'{output_filename_base}.csv')
    #     output_csv_path = os.path.join(project_root, 'strategies', 'RoC', 'backtest_results', f'{output_filename_base}_{param_str}.csv')
    # use above if you want to save the results with parameters


    os.makedirs(os.path.dirname(output_csv_path), exist_ok=True)
    
    run_full_market_backtest(market_data_path, output_csv_path, strategy_params, start_date, end_date)
    return output_csv_path

# --- Script Entry Point ---
if __name__ == "__main__":
    # strategy_params are now defined at the top of the script
    run_backtest_with_params(strategy_params)